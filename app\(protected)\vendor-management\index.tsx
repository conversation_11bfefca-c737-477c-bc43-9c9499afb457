import React, { useState, useEffect, useCallback, Fragment } from "react";
import { useTranslation } from "react-i18next";
import { router, useFocusEffect } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import { useDebounce } from "@/utils/useDebounce";
import {
  getVendorListAction,
  deleteVendorAction,
} from "@/store/actions/vendor";
import { VendorDetails, VendorListPayloadType } from "@/types/vendor";
import { Header, LoadingOverlay } from "@/components";
import { FloatingActionButton } from "@/components/atoms";
import { ConfirmationModal, SearchBar } from "@/components/molecules";
import SortBottomSheet, {
  SortOptionList,
} from "@/components/organisms/SortBottomSheet";
import { vendorSortOptions, vendorStatusList } from "@/utils/common";
import Toast from "react-native-toast-message";
import {
  Container,
  Content,
  SearchContainer,
  VendorListContainer,
  EmptyContainer,
  EmptyText,
  EmptyIcon,
  EmptySubtext,
  InfoRow,
  ItemsCountText,
  SortButtonRow,
  SortLabel,
  SortValueText,
  ActiveFiltersContainer,
  FilterChipsRow,
  ActiveFilterChip,
  ActiveFilterText,
} from "@/styles/VendorManagement.styles";
import { VendorList } from "@/components/organisms";
import {
  deleteVendor,
  resetFilters,
  resetVendorForm,
} from "@/store/slices/vendorSlice";
import StatusFilterBottomSheet from "@/components/organisms/StatusFilterBottomSheet";
import { UserType } from "@/types/api";
import { Redirect } from "expo-router";

export default function VendorManagementScreen() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const { user } = useAppSelector((state) => state.auth);

  // Redirect service persons and non-salespersons away from vendor management
  const isServicePerson = user?.role_id === UserType.SERVICEPERSON;
  const isSalesPerson = user?.role_id === UserType.SALESPERSON;
  if (isServicePerson || !isSalesPerson) {
    return <Redirect href="/(protected)/(tabs)/home" />;
  }
  const dispatch = useAppDispatch();
  const {
    vendorList,
    isVendorListLoading,
    isLoadingMore,
    isDeleteLoading,
    filters,
    current_page,
    last_page,
  } = useAppSelector((state) => state.vendor);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<VendorDetails | null>(
    null
  );
  const [sortSheetVisible, setSortSheetVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState<SortOptionList | null>(null);
  const [statusSheetVisible, setStatusSheetVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  // Add debounced search
  const debouncedSearch = useDebounce(searchQuery, 300);

  // Fetch vendors list from API
  const fetchVendors = useCallback(
    async (params: VendorListPayloadType) => {
      try {
        if (params.page === 1) setIsRefreshing(true);
        await dispatch(getVendorListAction(params)).unwrap();
      } catch (error: any) {
        Toast.show({
          type: "error",
          text1: error?.message || t("vendorManagement.loadError"),
        });
      } finally {
        setIsRefreshing(false);
      }
    },
    [dispatch, t]
  );

  // On mount, load first page
  useEffect(() => {
    fetchVendors({ page: 1 });
  }, [fetchVendors]);

  // Handle all filter and search changes
  useEffect(() => {
    // Skip if this is the initial render and no filters are set
    if (
      debouncedSearch === "" &&
      selectedStatus === null &&
      selectedSort === null
    )
      return;

    const payload: VendorListPayloadType = {
      page: 1,
    };

    if (debouncedSearch && debouncedSearch.length >= 3) {
      payload.search = debouncedSearch;
    }

    if (selectedStatus) payload.auth_dealer = selectedStatus;
    if (selectedSort?.order_by) {
      payload.order_by = selectedSort.order_by;
    }
    if (selectedSort?.sort_by) {
      payload.sort_by = selectedSort.sort_by;
    }

    fetchVendors(payload);
  }, [debouncedSearch, selectedStatus, selectedSort, fetchVendors]);

  useFocusEffect(
    useCallback(() => {
      // Reset vendor form data when returning to this screen to ensure clean forms
      dispatch(resetVendorForm());
    }, [dispatch])
  );

  const handleSearchInputChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleAddVendor = useCallback(() => {
    router.push("/addvendor");
  }, []);

  const handleEditVendor = useCallback(
    async (vendor: VendorDetails) => {
      setSearchQuery("");
      setSelectedSort(null);
      setSelectedStatus(null);
      dispatch(resetFilters());
      router.push(`/addvendor?vendorId=${vendor.vendor_id}`);
    },
    [router, dispatch]
  );

  const handleDeleteVendor = useCallback((vendor: VendorDetails) => {
    setSelectedVendor(vendor);
    setShowDeleteModal(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!selectedVendor?.vendor_id) return;
    try {
      const response = await dispatch(
        deleteVendorAction({ vendor_id: selectedVendor.vendor_id })
      ).unwrap();
      if (response.status) {
        dispatch(deleteVendor({ id: selectedVendor.vendor_id }));
        Toast.show({
          type: "success",
          text1: response.message || t("vendorManagement.deleteSuccess"),
        });
        setShowDeleteModal(false);
        setSelectedVendor(null);
      } else {
        Toast.show({
          type: "error",
          text1: response.message || t("vendorManagement.deleteError"),
        });
      }
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error?.message || t("vendorManagement.deleteError"),
      });
    }
  }, [selectedVendor, dispatch, t]);

  const renderEmptyState = () => (
    <EmptyContainer>
      <EmptyIcon>
        <Ionicons
          name="people-outline"
          size={80}
          color={theme.colors.secondary}
        />
      </EmptyIcon>
      <EmptyText>
        {searchQuery
          ? t("vendorManagement.noSearchResults")
          : t("vendorManagement.noVendors")}
      </EmptyText>
      <EmptySubtext>
        {searchQuery
          ? t("vendorManagement.tryDifferentSearch")
          : t("vendorManagement.addFirstVendor")}
      </EmptySubtext>
    </EmptyContainer>
  );

  const handleStatusPress = () => setStatusSheetVisible(true);

  const handleStatusDismiss = () => setStatusSheetVisible(false);

  const handleStatusClear = () => {
    handleStatusApply(null);
  };

  const handleStatusApply = async (statusId: number | null) => {
    const payload: VendorListPayloadType = {
      page: 1,
    };
    if (selectedSort?.order_by) {
      payload.order_by = selectedSort.order_by;
    }
    if (selectedSort?.sort_by) {
      payload.sort_by = selectedSort.sort_by;
    }
    if (statusId) payload.auth_dealer = statusId;
    setSelectedStatus(statusId);
    setStatusSheetVisible(false);
    await fetchVendors(payload);
  };

  // Sort handlers
  const handleSortPress = () => setSortSheetVisible(true);

  const handleSortClear = () => {
    setSelectedSort(null);
    setSortSheetVisible(false);
    const payload: VendorListPayloadType = {
      page: 1,
    };
    if (selectedStatus) payload.auth_dealer = selectedStatus;
    fetchVendors(payload);
  };

  const handleSortDismiss = () => setSortSheetVisible(false);

  const handleSortApply = (sortType: SortOptionList | null) => {
    setSelectedSort(sortType);
    setSortSheetVisible(false);
    const payload: VendorListPayloadType = {
      page: 1,
    };
    if (selectedStatus) payload.auth_dealer = selectedStatus;
    if (sortType?.order_by) {
      payload.order_by = sortType.order_by;
    }
    if (sortType?.sort_by) {
      payload.sort_by = sortType.sort_by;
    }
    fetchVendors(payload);
  };

  // Refresh and load more handlers
  const handleRefresh = useCallback(() => {
    setSearchQuery("");
    setSelectedSort(null);
    setSelectedStatus(null);
    dispatch(resetFilters());
    fetchVendors({
      page: 1,
    });
  }, [fetchVendors, dispatch]);

  const handleLoadMore = useCallback(() => {
    if (!isLoadingMore && current_page < last_page) {
      const payload: VendorListPayloadType = {
        page: current_page + 1,
      };
      if (searchQuery) payload.search = searchQuery;
      if (selectedStatus) payload.auth_dealer = selectedStatus;
      if (selectedSort?.order_by) {
        payload.order_by = selectedSort.order_by;
      }
      if (selectedSort?.sort_by) {
        payload.sort_by = selectedSort.sort_by;
      }
      fetchVendors(payload);
    }
  }, [
    isLoadingMore,
    current_page,
    last_page,
    fetchVendors,
    searchQuery,
    selectedStatus,
    selectedSort,
  ]);

  const vendors = Array.isArray(vendorList) ? vendorList : [];
  const hasVendors = vendors.length > 0;
  const renderActiveFilters = () => {
    const activeFilters = [];

    if (selectedStatus !== null) {
      const status = vendorStatusList.find((s) => s.id === selectedStatus);
      if (status) {
        activeFilters.push({
          label: status.label,
          onRemove: () => handleStatusApply(null),
        });
      }
    }

    if (activeFilters.length === 0) return null;

    return (
      <ActiveFiltersContainer>
        <FilterChipsRow>
          {activeFilters.map((filter, index) => (
            <ActiveFilterChip key={index} onPress={filter.onRemove}>
              <ActiveFilterText>{filter.label}</ActiveFilterText>
              <Ionicons name="close" size={14} color={theme.colors.white} />
            </ActiveFilterChip>
          ))}
        </FilterChipsRow>
      </ActiveFiltersContainer>
    );
  };

  if (isVendorListLoading && !isRefreshing && current_page === 1) {
    return <LoadingOverlay isLoading={isVendorListLoading} />;
  }

  return (
    <Fragment>
      <Header title={t("vendorManagement.title")} showBack={true} />

      <Container>
        <SearchContainer>
          <SearchBar
            onSearch={handleSearchInputChange}
            onFilterPress={handleStatusPress}
            value={searchQuery}
          />
        </SearchContainer>

        {/* Info Row with Sort */}
        {hasVendors && (
          <InfoRow>
            <ItemsCountText>
              {vendors.length} {t("vendorManagement.vendors")}
            </ItemsCountText>
            <SortButtonRow onPress={handleSortPress}>
              <SortLabel>{t("Sort by")}: </SortLabel>
              <SortValueText>
                {selectedSort?.label || t("Select")}
              </SortValueText>
              <Ionicons
                name="chevron-down"
                size={16}
                color={theme.colors.primary}
                style={{ marginLeft: 2 }}
              />
            </SortButtonRow>
          </InfoRow>
        )}

        {renderActiveFilters()}

        <Content>
          {hasVendors ? (
            <VendorListContainer>
              <VendorList
                vendors={vendors}
                onEdit={handleEditVendor}
                onDelete={handleDeleteVendor}
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                onEndReached={handleLoadMore}
                loading={isVendorListLoading}
                loadingMore={isLoadingMore}
              />
            </VendorListContainer>
          ) : (
            renderEmptyState()
          )}
        </Content>
      </Container>

      <FloatingActionButton onPress={handleAddVendor} icon="add" />

      <ConfirmationModal
        visible={showDeleteModal}
        title={t("vendorManagement.deleteTitle")}
        message={t("vendorManagement.deleteMessage", {
          name: `${selectedVendor?.first_name} ${selectedVendor?.last_name}`,
        })}
        variant="danger"
        icon="trash-outline"
        confirmText={t("common.delete")}
        cancelText={t("common.cancel")}
        onCancel={() => {
          setShowDeleteModal(false);
          setSelectedVendor(null);
        }}
        onConfirm={confirmDelete}
        loading={isDeleteLoading}
      />

      {/* Sort Bottom Sheet */}
      <SortBottomSheet
        isVisible={sortSheetVisible}
        onClose={handleSortDismiss}
        onApply={handleSortApply}
        onDismiss={handleSortDismiss}
        onClear={handleSortClear}
        sortOptions={vendorSortOptions}
        title={t("Sort by")}
      />
      {/* Status Filter Bottom Sheet */}
      <StatusFilterBottomSheet
        filters={filters}
        isVisible={statusSheetVisible}
        onDismiss={handleStatusDismiss}
        statusOptions={vendorStatusList}
        onClear={handleStatusClear}
        onApply={handleStatusApply}
        selectedkey={"auth_dealer"}
      />
    </Fragment>
  );
}
